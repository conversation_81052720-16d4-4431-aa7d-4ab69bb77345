use std::time::Duration;
use windows::core::GUID;
use velocitun::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("VelociTun Rust Example");

    // Generate a random GUID for the adapter
    let adapter_guid = GUID {
        data1: 0x12345678,
        data2: 0x1234,
        data3: 0x5678,
        data4: [0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0],
    };

    println!("Creating VelociTun adapter...");

    // Create adapter names as wide strings
    let adapter_name = "TestAdapter\0".encode_utf16().collect::<Vec<u16>>();
    let tunnel_type = "VelociTun\0".encode_utf16().collect::<Vec<u16>>();

    // Create the adapter
    let adapter = unsafe {
        VelociTunCreateAdapter(
            windows::core::PCWSTR(adapter_name.as_ptr()),
            windows::core::PCWSTR(tunnel_type.as_ptr()),
            &adapter_guid,
        )
    };

    if adapter.is_null() {
        println!("Failed to create adapter");
        return Err("Failed to create adapter".into());
    }

    println!("Adapter created successfully!");

    // Start a session
    println!("Starting session...");
    let session = unsafe {
        VelociTunStartSession(adapter, 0x400000) // 4MB ring buffer
    };

    if session.is_null() {
        println!("Failed to start session");
        unsafe {
            VelociTunCloseAdapter(adapter);
        }
        return Err("Failed to start session".into());
    }

    println!("Session started successfully!");

    // Get the read wait event
    let read_event = unsafe { VelociTunGetReadWaitEvent(session) };
    println!("Got read wait event: {:?}", read_event);

    // Demonstrate packet allocation and sending
    println!("Allocating send packet...");
    let packet_size = 64; // Small test packet
    let send_packet = unsafe { VelociTunAllocateSendPacket(session, packet_size) };

    if !send_packet.is_null() {
        println!("Send packet allocated, filling with test data...");

        // Fill packet with test data
        unsafe {
            let packet_data = std::slice::from_raw_parts_mut(send_packet, packet_size as usize);
            for (i, byte) in packet_data.iter_mut().enumerate() {
                *byte = (i % 256) as u8;
            }

            // Send the packet
            VelociTunSendPacket(session, send_packet);
            println!("Test packet sent!");
        }
    } else {
        println!("Failed to allocate send packet");
    }

    // Try to receive a packet (will likely timeout since we're not connected)
    println!("Attempting to receive packet...");
    let mut packet_size = 0u32;
    let received_packet = unsafe { VelociTunReceivePacket(session, &mut packet_size) };

    if !received_packet.is_null() {
        println!("Received packet of size: {}", packet_size);
        unsafe {
            VelociTunReleaseReceivePacket(session, received_packet);
        }
    } else {
        println!("No packet received (expected for test)");
    }

    // Clean up
    println!("Cleaning up...");
    unsafe {
        VelociTunEndSession(session);
        VelociTunCloseAdapter(adapter);
    }

    println!("Example completed successfully!");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_adapter_creation() {
        // This is a basic test that would need to run with admin privileges
        // and on a Windows system with the actual driver

        let adapter_guid = GUID {
            data1: 0x87654321,
            data2: 0x4321,
            data3: 0x8765,
            data4: [0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10],
        };

        let adapter_name = "TestAdapter\0".encode_utf16().collect::<Vec<u16>>();
        let tunnel_type = "VelociTun\0".encode_utf16().collect::<Vec<u16>>();

        let adapter = unsafe {
            VelociTunCreateAdapter(
                windows::core::PCWSTR(adapter_name.as_ptr()),
                windows::core::PCWSTR(tunnel_type.as_ptr()),
                &adapter_guid,
            )
        };

        // In a real test environment, this should succeed
        // For CI/testing without actual drivers, it will fail gracefully

        if !adapter.is_null() {
            unsafe {
                VelociTunCloseAdapter(adapter);
            }
        }
    }

    #[test]
    fn test_driver_version() {
        // Test getting driver version (should work even without driver installed)
        let version = unsafe { VelociTunGetRunningDriverVersion() };

        // Version 0 means driver not loaded, which is expected in test environment
        println!("Driver version: 0x{:08X}", version);
    }

    #[test]
    fn test_error_handling() {
        // Test error handling with invalid parameters
        let adapter = unsafe {
            VelociTunCreateAdapter(
                windows::core::PCWSTR::null(),
                windows::core::PCWSTR::null(),
                std::ptr::null(),
            )
        };

        // Should return null for invalid parameters
        assert!(adapter.is_null());
    }
}
