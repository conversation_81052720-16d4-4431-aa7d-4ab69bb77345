[Version]
Signature = "$Windows NT$"
Class = Net
ClassGUID = {4d36e972-e325-11ce-bfc1-08002be10318}
Provider = %ManufacturerName%
CatalogFile = wintun.cat
DriverVer = 01/01/2024,********
PnpLockdown = 1

[Manufacturer]
%ManufacturerName% = Standard,NTarm64

[Standard.NTarm64]
%DeviceDescription% = wintun.ndi, Wintun

[wintun.ndi]
Characteristics = 0x1
*IfType = 0x1
*MediaType = 0x0
*PhysicalMediaType = 0x0
AddReg = wintun.ndi.AddReg
CopyFiles = wintun.CopyFiles

[wintun.ndi.AddReg]
HKR, , BusNumber, 0, "0"

[wintun.CopyFiles]
wintun.sys,,,2

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
wintun.sys = 1,,

[DestinationDirs]
wintun.CopyFiles = 12

[Strings]
ManufacturerName = "Wintun"
DeviceDescription = "Wintun Userspace Tunnel"
DiskName = "Wintun Driver Disk"