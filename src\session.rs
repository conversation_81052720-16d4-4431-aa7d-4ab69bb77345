use crate::adapter::Adapter;
use crate::error::{VelociTunError, VelociTunResult};
use crate::logger::*;
use crate::packet::Packet;
use crate::ring::Ring;
use std::sync::Arc;
use std::time::Duration;
use windows::Win32::Foundation::CloseHandle;
use windows::Win32::Foundation::{ERROR_TIMEOUT, FALSE, HANDLE, WAIT_OBJECT_0, WAIT_TIMEOUT};
use windows::Win32::System::Threading::{CreateEventW, SetEvent, WaitForSingleObject};

/// VelociTun session for packet I/O
pub struct Session {
    adapter: Arc<Adapter>,
    send_ring: Arc<Ring>,
    receive_ring: Arc<Ring>,
    read_wait_event: HANDLE,
    capacity: u32,
    active: std::sync::atomic::AtomicBool,
}

impl Session {
    /// Create a new session for the given adapter
    pub(crate) fn new(adapter: &Adapter, capacity: u32) -> VelociTunResult<Self> {
        Self::validate_capacity(capacity)?;

        log_info!(
            "Creating session for adapter '{}' with capacity {}",
            adapter.name(),
            capacity
        );

        let send_ring = Ring::new(capacity)?;
        let receive_ring = Ring::new(capacity)?;

        let read_wait_event = unsafe {
            CreateEventW(None, FALSE, FALSE, None).map_err(|e| VelociTunError::WindowsApi(e))?
        };

        Ok(Session {
            adapter: Arc::new(adapter.clone()),
            send_ring: Arc::new(send_ring),
            receive_ring: Arc::new(receive_ring),
            read_wait_event,
            capacity,
            active: std::sync::atomic::AtomicBool::new(true),
        })
    }

    /// Get the adapter associated with this session
    pub fn adapter(&self) -> &Adapter {
        &self.adapter
    }

    /// Get the ring buffer capacity
    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    /// Check if the session is active
    pub fn is_active(&self) -> bool {
        self.active.load(std::sync::atomic::Ordering::Acquire)
    }

    /// Send a packet
    pub fn send_packet(&self, data: &[u8]) -> VelociTunResult<()> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        if data.len() > crate::packet::MAX_IP_PACKET_SIZE {
            return Err(VelociTunError::InvalidParameter(
                "Packet too large".to_string(),
            ));
        }

        // Create packet
        let packet = Packet::new(data)?;

        // Try to allocate space in send ring
        if self.send_ring.available_space() < packet.total_size() as u32 {
            return Err(VelociTunError::BufferOverflow);
        }

        // Write packet to ring
        self.send_ring.write_packet(&packet)?;

        log_info!("Sent packet of {} bytes", data.len());
        Ok(())
    }

    /// Receive a packet (blocking)
    pub fn receive_packet(&self) -> VelociTunResult<Vec<u8>> {
        self.receive_packet_timeout(Duration::from_secs(u64::MAX))
    }

    /// Receive a packet with timeout
    pub fn receive_packet_timeout(&self, timeout: Duration) -> VelociTunResult<Vec<u8>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        let start_time = std::time::Instant::now();

        loop {
            // Try to read from ring
            if let Ok(packet) = self.receive_ring.read_packet() {
                log_info!("Received packet of {} bytes", packet.data().len());
                return Ok(packet.data().to_vec());
            }

            // Check timeout
            if start_time.elapsed() >= timeout {
                return Err(VelociTunError::WindowsApi(windows::core::Error::from(
                    ERROR_TIMEOUT,
                )));
            }

            // Wait for data or timeout
            unsafe {
                let wait_result = WaitForSingleObject(self.read_wait_event, 100);
                match wait_result {
                    WAIT_OBJECT_0 => continue, // Event signaled, try again
                    WAIT_TIMEOUT => continue,  // Keep waiting
                    _ => {
                        return Err(VelociTunError::WindowsApi(
                            windows::core::Error::from_win32(),
                        ))
                    }
                }
            }
        }
    }

    /// Try to receive a packet without blocking
    pub fn try_receive_packet(&self) -> VelociTunResult<Option<Vec<u8>>> {
        if !self.is_active() {
            return Err(VelociTunError::HandleEof);
        }

        match self.receive_ring.read_packet() {
            Ok(packet) => {
                log_info!("Received packet of {} bytes", packet.data().len());
                Ok(Some(packet.data().to_vec()))
            }
            Err(VelociTunError::NoMoreItems) => Ok(None),
            Err(e) => Err(e),
        }
    }

    /// Get statistics about the session
    pub fn stats(&self) -> SessionStats {
        SessionStats {
            send_ring_used: self.send_ring.used_space(),
            send_ring_available: self.send_ring.available_space(),
            receive_ring_used: self.receive_ring.used_space(),
            receive_ring_available: self.receive_ring.available_space(),
            capacity: self.capacity,
            is_active: self.is_active(),
        }
    }

    /// Shutdown the session
    pub fn shutdown(&self) {
        log_info!(
            "Shutting down session for adapter '{}'",
            self.adapter.name()
        );

        self.active
            .store(false, std::sync::atomic::Ordering::Release);

        // Signal any waiting threads
        unsafe {
            let _ = SetEvent(self.read_wait_event);
        }
    }

    /// Create a packet sender (for async/threaded usage)
    pub fn packet_sender(&self) -> PacketSender {
        PacketSender { session: self }
    }

    /// Create a packet receiver (for async/threaded usage)
    pub fn packet_receiver(&self) -> PacketReceiver {
        PacketReceiver { session: self }
    }

    fn validate_capacity(capacity: u32) -> VelociTunResult<()> {
        if !capacity.is_power_of_two() {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        if capacity < crate::ring::MIN_RING_CAPACITY || capacity > crate::ring::MAX_RING_CAPACITY {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        Ok(())
    }
}

impl Drop for Session {
    fn drop(&mut self) {
        self.shutdown();

        unsafe {
            if !self.read_wait_event.is_invalid() {
                let _ = CloseHandle(self.read_wait_event);
            }
        }

        log_info!("Session dropped for adapter '{}'", self.adapter.name());
    }
}

/// Session statistics
#[derive(Debug, Clone)]
pub struct SessionStats {
    pub send_ring_used: u32,
    pub send_ring_available: u32,
    pub receive_ring_used: u32,
    pub receive_ring_available: u32,
    pub capacity: u32,
    pub is_active: bool,
}

/// Packet sender for threaded usage
pub struct PacketSender<'a> {
    session: &'a Session,
}

impl<'a> PacketSender<'a> {
    pub fn send(&self, data: &[u8]) -> VelociTunResult<()> {
        self.session.send_packet(data)
    }

    pub fn is_active(&self) -> bool {
        self.session.is_active()
    }
}

/// Packet receiver for threaded usage
pub struct PacketReceiver<'a> {
    session: &'a Session,
}

impl<'a> PacketReceiver<'a> {
    pub fn receive(&self) -> VelociTunResult<Vec<u8>> {
        self.session.receive_packet()
    }

    pub fn receive_timeout(&self, timeout: Duration) -> VelociTunResult<Vec<u8>> {
        self.session.receive_packet_timeout(timeout)
    }

    pub fn try_receive(&self) -> VelociTunResult<Option<Vec<u8>>> {
        self.session.try_receive_packet()
    }

    pub fn is_active(&self) -> bool {
        self.session.is_active()
    }
}

// Implement Clone for Adapter since it's referenced in Session
impl Clone for Adapter {
    fn clone(&self) -> Self {
        Self {
            luid: self.luid,
            name: self.name.clone(),
            tunnel_type: self.tunnel_type.clone(),
            guid: self.guid,
        }
    }
}
