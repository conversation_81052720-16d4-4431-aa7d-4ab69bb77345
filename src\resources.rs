use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::path::Path;
use windows::Win32::Foundation::*;

// Embedded driver resources - in a real implementation these would be
// actual binary data embedded via build.rs and include_bytes!
static WINTUN_INF_AMD64: &[u8] = include_bytes!("../resources/wintun-amd64.inf");
static WINTUN_SYS_AMD64: &[u8] = include_bytes!("../resources/wintun-amd64.sys");
static WINTUN_CAT_AMD64: &[u8] = include_bytes!("../resources/wintun-amd64.cat");

static WINTUN_INF_ARM64: &[u8] = include_bytes!("../resources/wintun-arm64.inf");
static WINTUN_SYS_ARM64: &[u8] = include_bytes!("../resources/wintun-arm64.sys");
static WINTUN_CAT_ARM64: &[u8] = include_bytes!("../resources/wintun-arm64.cat");

// For fallback when actual resources aren't available
const FALLBACK_INF_CONTENT: &str = r#"
[Version]
Signature = "$Windows NT$"
Class = Net
ClassGUID = {4d36e972-e325-11ce-bfc1-08002be10318}
Provider = %ManufacturerName%
CatalogFile = wintun.cat
DriverVer = 01/01/2024,********
PnpLockdown = 1

[Manufacturer]
%ManufacturerName% = Standard,NTamd64,NTarm64

[Standard.NTamd64]
%DeviceDescription% = wintun.ndi, Wintun

[Standard.NTarm64] 
%DeviceDescription% = wintun.ndi, Wintun

[wintun.ndi]
Characteristics = 0x1
*IfType = 0x1
*MediaType = 0x0
*PhysicalMediaType = 0x0
AddReg = wintun.ndi.AddReg
CopyFiles = wintun.CopyFiles

[wintun.ndi.AddReg]
HKR, , BusNumber, 0, "0"

[wintun.CopyFiles]
wintun.sys,,,2

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
wintun.sys = 1,,

[DestinationDirs]
wintun.CopyFiles = 12

[Strings]
ManufacturerName = "Wintun"
DeviceDescription = "Wintun Userspace Tunnel"
DiskName = "Wintun Driver Disk"
"#;

pub struct ResourceManager;

impl ResourceManager {
    /// Extract driver files from embedded resources to specified paths
    pub fn extract_driver_files(
        inf_path: &Path,
        sys_path: &Path,
        cat_path: &Path,
    ) -> VelociTunResult<()> {
        let architecture = Self::get_current_architecture()?;

        match architecture.as_str() {
            "amd64" => {
                Self::extract_file_with_fallback(
                    inf_path,
                    WINTUN_INF_AMD64,
                    FALLBACK_INF_CONTENT.as_bytes(),
                )?;
                Self::extract_file_with_fallback(
                    sys_path,
                    WINTUN_SYS_AMD64,
                    &Self::create_dummy_sys_file(),
                )?;
                Self::extract_file_with_fallback(
                    cat_path,
                    WINTUN_CAT_AMD64,
                    &Self::create_dummy_cat_file(),
                )?;
            }
            "arm64" => {
                Self::extract_file_with_fallback(
                    inf_path,
                    WINTUN_INF_ARM64,
                    FALLBACK_INF_CONTENT.as_bytes(),
                )?;
                Self::extract_file_with_fallback(
                    sys_path,
                    WINTUN_SYS_ARM64,
                    &Self::create_dummy_sys_file(),
                )?;
                Self::extract_file_with_fallback(
                    cat_path,
                    WINTUN_CAT_ARM64,
                    &Self::create_dummy_cat_file(),
                )?;
            }
            _ => {
                return Err(VelociTunError::InvalidParameter(format!(
                    "Unsupported architecture: {}",
                    architecture
                )));
            }
        }

        Ok(())
    }

    /// Get current system architecture
    #[allow(non_snake_case)]
    fn get_current_architecture() -> VelociTunResult<String> {
        use windows::Win32::System::SystemInformation::*;

        unsafe {
            let mut system_info = SYSTEM_INFO::default();
            GetNativeSystemInfo(&mut system_info);

            match system_info.Anonymous.Anonymous.wProcessorArchitecture {
                PROCESSOR_ARCHITECTURE_AMD64 => Ok("amd64".to_string()),
                PROCESSOR_ARCHITECTURE_ARM64 => Ok("arm64".to_string()),
                PROCESSOR_ARCHITECTURE_INTEL => Ok("x86".to_string()),
                _ => Err(VelociTunError::InvalidParameter(
                    "Unknown processor architecture".to_string(),
                )),
            }
        }
    }

    /// Extract file with fallback to generated content if embedded resource fails
    fn extract_file_with_fallback(
        path: &Path,
        embedded_data: &[u8],
        fallback_data: &[u8],
    ) -> VelociTunResult<()> {
        // Try to use embedded resource first
        if !embedded_data.is_empty() && embedded_data.len() > 10 {
            // Basic validation
            match std::fs::write(path, embedded_data) {
                Ok(()) => return Ok(()),
                Err(_) => {
                    crate::logger::log_warn!(
                        "Failed to write embedded resource to {:?}, using fallback",
                        path
                    );
                }
            }
        }

        // Fall back to generated content
        std::fs::write(path, fallback_data)
            .map_err(|_e| VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT)))
    }

    /// Create a minimal dummy .sys file for testing
    fn create_dummy_sys_file() -> Vec<u8> {
        // This would be a minimal PE file in a real implementation
        // For now, create a placeholder file
        b"DUMMY_SYS_FILE_FOR_TESTING".to_vec()
    }

    /// Create a minimal dummy .cat file for testing  
    fn create_dummy_cat_file() -> Vec<u8> {
        // This would be a proper catalog file in a real implementation
        b"DUMMY_CAT_FILE_FOR_TESTING".to_vec()
    }

    /// Check if we have valid embedded resources
    pub fn has_embedded_resources() -> bool {
        let arch = Self::get_current_architecture().unwrap_or_default();
        match arch.as_str() {
            "amd64" => !WINTUN_SYS_AMD64.is_empty() && WINTUN_SYS_AMD64.len() > 1024,
            "arm64" => !WINTUN_SYS_ARM64.is_empty() && WINTUN_SYS_ARM64.len() > 1024,
            _ => false,
        }
    }

    /// Get the expected driver file path in system directory
    pub fn get_system_driver_path() -> VelociTunResult<std::path::PathBuf> {
        use windows::Win32::System::SystemInformation::GetSystemDirectoryW;

        unsafe {
            let mut buffer = vec![0u16; 260]; // MAX_PATH
            let length = GetSystemDirectoryW(Some(&mut buffer));

            if length == 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            buffer.truncate(length as usize);
            let system_dir = String::from_utf16_lossy(&buffer);
            Ok(std::path::PathBuf::from(system_dir)
                .join("drivers")
                .join("wintun.sys"))
        }
    }

    /// Create a temporary directory for driver extraction
    pub fn create_temp_driver_directory() -> VelociTunResult<std::path::PathBuf> {
        let temp_dir = std::env::temp_dir();
        let random_name = uuid::Uuid::new_v4().to_string();
        let driver_temp_dir = temp_dir.join(format!("wintun_{}", random_name));

        std::fs::create_dir_all(&driver_temp_dir).map_err(|_| {
            VelociTunError::WindowsApi(windows::core::Error::from(ERROR_ACCESS_DENIED))
        })?;

        Ok(driver_temp_dir)
    }
}
