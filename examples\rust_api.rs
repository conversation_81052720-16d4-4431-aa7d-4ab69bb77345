use std::time::Duration;
use velocitun::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize the library
    velocitun::init();

    println!("VelociTun Rust Library v{}", velocitun::version());
    println!(
        "Platform: {}",
        if velocitun::is_windows() {
            "Windows"
        } else {
            "Other"
        }
    );

    // Ensure driver is available
    match velocitun::ensure_driver() {
        Ok(()) => println!("Driver is available"),
        Err(e) => println!("Driver not available: {}", e),
    }

    // List existing adapters
    match velocitun::list_adapters() {
        Ok(adapters) => {
            println!("Found {} existing adapters:", adapters.len());
            for adapter in adapters {
                println!("  - {} ({})", adapter.name(), adapter.tunnel_type());
            }
        }
        Err(e) => println!("Could not list adapters: {}", e),
    }

    // Create a new adapter
    println!("\nCreating new adapter...");
    let adapter = match AdapterBuilder::new()
        .name("MyTunnelAdapter")
        .tunnel_type("VelociTun")
        .build()
    {
        Ok(adapter) => {
            println!("Created adapter: {}", adapter.name());
            adapter
        }
        Err(e) => {
            println!("Failed to create adapter: {}", e);
            return Ok(());
        }
    };

    // Start a session
    println!("Starting session...");
    let session = match adapter.start_session(0x400000) {
        // 4MB
        Ok(session) => {
            println!("Session started with capacity: {}", session.capacity());
            session
        }
        Err(e) => {
            println!("Failed to start session: {}", e);
            return Ok(());
        }
    };

    // Create test packet
    let test_data = create_test_packet();

    // Send packet
    println!("Sending test packet...");
    match session.send_packet(&test_data) {
        Ok(()) => println!("Packet sent successfully"),
        Err(e) => println!("Failed to send packet: {}", e),
    }

    // Try to receive packet (non-blocking)
    println!("Attempting to receive packet...");
    match session.try_receive_packet() {
        Ok(Some(data)) => {
            println!("Received packet: {} bytes", data.len());
            print_packet_info(&data);
        }
        Ok(None) => println!("No packet available"),
        Err(e) => println!("Error receiving packet: {}", e),
    }

    // Show session statistics
    let stats = session.stats();
    println!("\nSession Statistics:");
    println!(
        "  Send ring: {}/{} used",
        stats.send_ring_used,
        stats.send_ring_used + stats.send_ring_available
    );
    println!(
        "  Receive ring: {}/{} used",
        stats.receive_ring_used,
        stats.receive_ring_used + stats.receive_ring_available
    );
    println!("  Active: {}", stats.is_active);

    // Demonstrate packet sender/receiver
    println!("\nDemonstrating threaded packet handling...");
    demonstrate_threaded_io(&session);

    println!("\nExample completed successfully!");
    Ok(())
}

fn create_test_packet() -> Vec<u8> {
    // Create a simple IPv4 packet (ping)
    let mut packet = vec![0u8; 28]; // IP header (20) + ICMP header (8)

    // IPv4 header
    packet[0] = 0x45; // Version 4, IHL 5
    packet[1] = 0x00; // DSCP, ECN
    packet[2..4].copy_from_slice(&28u16.to_be_bytes()); // Total length
    packet[4..6].copy_from_slice(&0x1234u16.to_be_bytes()); // Identification
    packet[6..8].copy_from_slice(&0x4000u16.to_be_bytes()); // Flags, Fragment offset
    packet[8] = 64; // TTL
    packet[9] = 1; // Protocol (ICMP)
    packet[10..12].copy_from_slice(&0u16.to_be_bytes()); // Header checksum (to be calculated)
    packet[12..16].copy_from_slice(&[192, 168, 1, 1]); // Source IP
    packet[16..20].copy_from_slice(&[8, 8, 8, 8]); // Destination IP

    // ICMP header
    packet[20] = 8; // Type (Echo Request)
    packet[21] = 0; // Code
    packet[22..24].copy_from_slice(&0u16.to_be_bytes()); // Checksum (to be calculated)
    packet[24..26].copy_from_slice(&0x1234u16.to_be_bytes()); // Identifier
    packet[26..28].copy_from_slice(&0x0001u16.to_be_bytes()); // Sequence number

    packet
}

fn print_packet_info(data: &[u8]) {
    if data.len() < 20 {
        println!("  Invalid packet (too short)");
        return;
    }

    let version = (data[0] >> 4) & 0xF;
    if version == 4 {
        let src = &data[12..16];
        let dst = &data[16..20];
        let protocol = data[9];

        println!("  IPv4 packet:");
        println!("    Source: {}.{}.{}.{}", src[0], src[1], src[2], src[3]);
        println!(
            "    Destination: {}.{}.{}.{}",
            dst[0], dst[1], dst[2], dst[3]
        );
        println!("    Protocol: {}", protocol);
    } else {
        println!("  Non-IPv4 packet (version: {})", version);
    }
}

fn demonstrate_threaded_io(session: &Session) {
    use std::sync::Arc;
    use std::thread;

    let session = Arc::new(session);

    // Sender thread
    let sender_session = session.clone();
    let sender_handle = thread::spawn(move || {
        let sender = sender_session.packet_sender();

        for i in 0..3 {
            let mut packet = create_test_packet();
            packet[27] = i as u8; // Vary sequence number

            match sender.send(&packet) {
                Ok(()) => println!("  Sent packet {}", i + 1),
                Err(e) => println!("  Failed to send packet {}: {}", i + 1, e),
            }

            thread::sleep(Duration::from_millis(100));
        }
    });

    // Receiver thread
    let receiver_session = session.clone();
    let receiver_handle = thread::spawn(move || {
        let receiver = receiver_session.packet_receiver();

        for i in 0..3 {
            match receiver.receive_timeout(Duration::from_millis(500)) {
                Ok(data) => println!("  Received packet {}: {} bytes", i + 1, data.len()),
                Err(e) => println!("  Receive timeout/error {}: {}", i + 1, e),
            }
        }
    });

    // Wait for threads to complete
    let _ = sender_handle.join();
    let _ = receiver_handle.join();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_library_initialization() {
        velocitun::init();
        assert!(velocitun::version().len() > 0);
    }

    #[test]
    fn test_adapter_builder() {
        let builder = AdapterBuilder::new()
            .name("TestAdapter")
            .tunnel_type("VelociTun");

        // This will fail without admin rights and actual driver
        // but tests the builder pattern
        let _ = builder.build();
    }

    #[test]
    fn test_packet_creation() {
        let data = vec![1, 2, 3, 4, 5];
        let packet = Packet::new(&data).unwrap();

        assert_eq!(packet.data(), &data);
        assert_eq!(packet.size(), 5);
    }

    #[test]
    fn test_packet_ip_parsing() {
        let packet_data = create_test_packet();
        let packet = Packet::new(&packet_data).unwrap();

        assert_eq!(packet.ip_version(), Some(4));
        assert_eq!(packet.source_ipv4(), Some([192, 168, 1, 1]));
        assert_eq!(packet.destination_ipv4(), Some([8, 8, 8, 8]));
        assert_eq!(packet.protocol(), Some(1)); // ICMP
    }
}
