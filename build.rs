use std::env;
use std::path::PathBuf;

fn main() {
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=resources/");

    let target = env::var("TARGET").unwrap();

    if target.contains("windows") {
        // Link with Windows libraries
        println!("cargo:rustc-link-lib=kernel32");
        println!("cargo:rustc-link-lib=advapi32");
        println!("cargo:rustc-link-lib=setupapi");
        println!("cargo:rustc-link-lib=newdev");
        println!("cargo:rustc-link-lib=iphlpapi");
        println!("cargo:rustc-link-lib=ws2_32");
        println!("cargo:rustc-link-lib=user32");
        println!("cargo:rustc-link-lib=version");

        // Set up WDF library linking if available
        if let Ok(wdk_path) = env::var("WDK_PATH") {
            let wdf_lib_path = format!("{}/lib/wdf/kmdf/x64/1.15", wdk_path);
            println!("cargo:rustc-link-search=native={}", wdf_lib_path);
            println!("cargo:rustc-link-lib=wdf01000");
        }

        // Ensure resource files exist
        ensure_resource_files();
    }
}

fn ensure_resource_files() {
    let resources_dir = PathBuf::from("resources");

    // Check if resource files exist, create empty ones if they don't
    let files = [
        "velocitun-amd64.inf",
        "velocitun-amd64.sys",
        "velocitun-amd64.cat",
        "velocitun-arm64.inf",
        "velocitun-arm64.sys",
        "velocitun-arm64.cat",
    ];

    for file in &files {
        let file_path = resources_dir.join(file);
        if !file_path.exists() {
            if let Some(parent) = file_path.parent() {
                let _ = std::fs::create_dir_all(parent);
            }

            // Create placeholder file
            let content = if file.ends_with(".inf") {
                create_default_inf_content()
            } else if file.ends_with(".sys") {
                b"PLACEHOLDER_DRIVER_FILE".to_vec()
            } else if file.ends_with(".cat") {
                b"PLACEHOLDER_CATALOG_FILE".to_vec()
            } else {
                Vec::new()
            };

            let _ = std::fs::write(&file_path, content);
            println!("Created placeholder resource file: {:?}", file_path);
        }
    }
}

fn create_default_inf_content() -> Vec<u8> {
    let inf_content = r#"[Version]
Signature = "$Windows NT$"
Class = Net
ClassGUID = {4d36e972-e325-11ce-bfc1-08002be10318}
Provider = %ManufacturerName%
CatalogFile = velocitun.cat
DriverVer = 01/01/2024,********
PnpLockdown = 1

[Manufacturer]
%ManufacturerName% = Standard,NTamd64,NTarm64

[Standard.NTamd64]
%DeviceDescription% = velocitun.ndi, VelociTun

[Standard.NTarm64]
%DeviceDescription% = velocitun.ndi, VelociTun

[velocitun.ndi]
Characteristics = 0x1
*IfType = 0x1
*MediaType = 0x0
*PhysicalMediaType = 0x0
AddReg = velocitun.ndi.AddReg
CopyFiles = velocitun.CopyFiles

[velocitun.ndi.AddReg]
HKR, , BusNumber, 0, "0"

[velocitun.CopyFiles]
velocitun.sys,,,2

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
velocitun.sys = 1,,

[DestinationDirs]
velocitun.CopyFiles = 12

[Strings]
ManufacturerName = "VelociTun"
DeviceDescription = "VelociTun High-Speed Network Tunnel"
DiskName = "VelociTun Driver Disk"
"#;
    inf_content.as_bytes().to_vec()
}
