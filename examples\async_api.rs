use std::time::Duration;
use tokio::time::sleep;
use velocitun::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    velocitun::init();

    println!("VelociTun Rust 异步示例");
    println!(
        "Platform: {}",
        if velocitun::is_windows() {
            "Windows"
        } else {
            "Other"
        }
    );

    // 确保驱动可用
    match velocitun::ensure_driver() {
        Ok(()) => println!("驱动已就绪"),
        Err(e) => {
            println!("驱动不可用: {}", e);
            return Ok(());
        }
    }

    // 创建适配器
    println!("创建适配器...");
    let adapter = match AdapterBuilder::new()
        .name("AsyncTunnelAdapter")
        .tunnel_type("VelociTun")
        .build()
    {
        Ok(adapter) => {
            println!("适配器创建成功: {}", adapter.name());
            adapter
        }
        Err(e) => {
            println!("适配器创建失败: {}", e);
            return Ok(());
        }
    };

    // 启动会话
    println!("启动会话...");
    let session = match adapter.start_session(0x400000) {
        // 4MB
        Ok(session) => {
            println!("会话启动成功，缓冲区大小: {}", session.capacity());
            session
        }
        Err(e) => {
            println!("会话启动失败: {}", e);
            return Ok(());
        }
    };

    // 创建异步会话包装器
    let async_session = AsyncSession::new(session);

    // 演示基本异步操作
    println!("\\n=== 基本异步操作演示 ===");
    demo_basic_async_operations(&async_session).await?;

    // 演示异步流处理
    println!("\\n=== 异步流处理演示 ===");
    demo_async_stream_processing(&async_session).await?;

    // 演示异步通道处理
    println!("\\n=== 异步通道处理演示 ===");
    demo_async_channel_processing(&async_session).await?;

    // 演示批量处理
    println!("\\n=== 批量处理演示 ===");
    demo_batch_processing(&async_session).await?;

    println!("\\n异步示例执行完成！");
    Ok(())
}

async fn demo_basic_async_operations(
    session: &AsyncSession,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("发送测试数据包...");

    // 创建测试数据包
    let test_packet = create_test_packet();

    // 异步发送数据包
    match session.send_packet(&test_packet).await {
        Ok(()) => println!("数据包发送成功"),
        Err(e) => println!("数据包发送失败: {}", e),
    }

    // 尝试异步接收数据包
    println!("尝试接收数据包...");
    match session.try_receive_packet().await {
        Ok(Some(data)) => {
            println!("接收到数据包: {} bytes", data.len());
            print_packet_info(&data);
        }
        Ok(None) => println!("当前没有数据包"),
        Err(e) => println!("接收错误: {}", e),
    }

    // 带超时的接收
    println!("带超时的接收测试...");
    match session
        .receive_packet_timeout(Duration::from_millis(100))
        .await
    {
        Ok(data) => {
            println!("超时接收成功: {} bytes", data.len());
        }
        Err(VelociTunError::Timeout) => println!("接收超时（预期行为）"),
        Err(e) => println!("接收错误: {}", e),
    }

    Ok(())
}

async fn demo_async_stream_processing(
    session: &AsyncSession,
) -> Result<(), Box<dyn std::error::Error>> {
    use futures::StreamExt;

    println!("启动异步流处理...");

    // 创建数据包流
    let mut stream = session.packet_stream();

    // 发送一些测试数据包
    tokio::spawn({
        let session = session.clone();
        async move {
            for i in 0..3 {
                let mut packet = create_test_packet();
                packet.push(i); // 添加序列号

                if let Err(e) = session.send_packet(&packet).await {
                    println!("发送数据包 {} 失败: {}", i, e);
                } else {
                    println!("发送数据包 {} 成功", i);
                }

                sleep(Duration::from_millis(50)).await;
            }
        }
    });

    // 处理流中的数据包
    let mut count = 0;
    let timeout_duration = Duration::from_secs(2);

    let stream_future = async {
        while let Some(result) = stream.next().await {
            match result {
                Ok(data) => {
                    count += 1;
                    println!("流处理: 收到数据包 {} ({} bytes)", count, data.len());
                    if count >= 3 {
                        break;
                    }
                }
                Err(e) => println!("流处理错误: {}", e),
            }
        }
    };

    // 带超时的流处理
    match tokio::time::timeout(timeout_duration, stream_future).await {
        Ok(()) => println!("流处理完成"),
        Err(_) => println!("流处理超时"),
    }

    Ok(())
}

async fn demo_async_channel_processing(
    session: &AsyncSession,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("启动异步通道处理...");

    // 创建通道
    let (sender, mut receiver) = session.create_packet_channel(10);

    // 发送任务
    let send_task = tokio::spawn(async move {
        for i in 0..5 {
            let mut packet = create_test_packet();
            packet.push(i); // 添加序列号

            match sender.send(&packet).await {
                Ok(()) => println!("通道发送: 数据包 {} 发送成功", i),
                Err(e) => println!("通道发送: 数据包 {} 发送失败: {}", i, e),
            }

            sleep(Duration::from_millis(100)).await;
        }

        println!("发送任务完成");
    });

    // 接收任务
    let receive_task = tokio::spawn(async move {
        let mut count = 0;

        while count < 5 {
            match receiver.receive_timeout(Duration::from_secs(2)).await {
                Ok(data) => {
                    count += 1;
                    println!("通道接收: 数据包 {} ({} bytes)", count, data.len());
                }
                Err(VelociTunError::Timeout) => {
                    println!("通道接收超时");
                    break;
                }
                Err(e) => {
                    println!("通道接收错误: {}", e);
                    break;
                }
            }
        }

        println!("接收任务完成");
    });

    // 等待任务完成
    let (send_result, receive_result) = tokio::join!(send_task, receive_task);

    if let Err(e) = send_result {
        println!("发送任务错误: {}", e);
    }

    if let Err(e) = receive_result {
        println!("接收任务错误: {}", e);
    }

    Ok(())
}

async fn demo_batch_processing(session: &AsyncSession) -> Result<(), Box<dyn std::error::Error>> {
    println!("启动批量处理演示...");

    let session_arc = std::sync::Arc::new(session.inner());
    let processor = AsyncPacketProcessor::new(session_arc);

    // 发送一批测试数据包
    let sender = session.async_packet_sender();
    let packets: Vec<Vec<u8>> = (0..10)
        .map(|i| {
            let mut packet = create_test_packet();
            packet.push(i); // 添加序列号
            packet
        })
        .collect();

    println!("发送 {} 个数据包...", packets.len());
    let packet_refs: Vec<&[u8]> = packets.iter().map(|p| p.as_slice()).collect();

    match sender.send_batch(&packet_refs).await {
        Ok(()) => println!("批量发送成功"),
        Err(e) => println!("批量发送失败: {}", e),
    }

    // 批量处理数据包
    let processed_count = std::sync::Arc::new(std::sync::atomic::AtomicUsize::new(0));

    let batch_future = {
        let processed_count = processed_count.clone();
        async move {
            processor
                .process_packet_batches(3, move |batch| {
                    let processed_count = processed_count.clone();
                    async move {
                        println!("处理批量数据包: {} 个", batch.len());
                        for (i, packet) in batch.iter().enumerate() {
                            println!("  批量处理数据包 {}: {} bytes", i + 1, packet.size());
                        }
                        processed_count
                            .fetch_add(batch.len(), std::sync::atomic::Ordering::Relaxed);
                        Ok(())
                    }
                })
                .await
        }
    };

    // 限制处理时间
    match tokio::time::timeout(Duration::from_secs(5), batch_future).await {
        Ok(Ok(())) => println!("批量处理完成"),
        Ok(Err(e)) => println!("批量处理错误: {}", e),
        Err(_) => println!("批量处理超时"),
    }

    let total_processed = processed_count.load(std::sync::atomic::Ordering::Relaxed);
    println!("共处理了 {} 个数据包", total_processed);

    Ok(())
}

fn create_test_packet() -> Vec<u8> {
    // 创建简单的 IPv4 数据包 (ping)
    let mut packet = vec![0u8; 28]; // IP 头 (20) + ICMP 头 (8)

    // IPv4 头
    packet[0] = 0x45; // Version 4, IHL 5
    packet[1] = 0x00; // DSCP, ECN
    packet[2..4].copy_from_slice(&28u16.to_be_bytes()); // Total length
    packet[4..6].copy_from_slice(&0x1234u16.to_be_bytes()); // Identification
    packet[6..8].copy_from_slice(&0x4000u16.to_be_bytes()); // Flags, Fragment offset
    packet[8] = 64; // TTL
    packet[9] = 1; // Protocol (ICMP)
    packet[10..12].copy_from_slice(&0u16.to_be_bytes()); // Header checksum
    packet[12..16].copy_from_slice(&[192, 168, 1, 1]); // Source IP
    packet[16..20].copy_from_slice(&[8, 8, 8, 8]); // Destination IP

    // ICMP 头
    packet[20] = 8; // Type (Echo Request)
    packet[21] = 0; // Code
    packet[22..24].copy_from_slice(&0u16.to_be_bytes()); // Checksum
    packet[24..26].copy_from_slice(&0x1234u16.to_be_bytes()); // Identifier
    packet[26..28].copy_from_slice(&0x0001u16.to_be_bytes()); // Sequence number

    packet
}

fn print_packet_info(data: &[u8]) {
    if data.len() < 20 {
        println!("  无效数据包（太短）");
        return;
    }

    let version = (data[0] >> 4) & 0xF;
    if version == 4 {
        let src = &data[12..16];
        let dst = &data[16..20];
        let protocol = data[9];

        println!("  IPv4 数据包:");
        println!("    源地址: {}.{}.{}.{}", src[0], src[1], src[2], src[3]);
        println!("    目标地址: {}.{}.{}.{}", dst[0], dst[1], dst[2], dst[3]);
        println!("    协议: {}", protocol);
    } else {
        println!("  非 IPv4 数据包 (版本: {})", version);
    }
}

impl Clone for AsyncSession {
    fn clone(&self) -> Self {
        Self {
            session: self.session.clone(),
            _runtime_handle: self._runtime_handle.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_async_session_creation() {
        velocitun::init();

        // 这个测试需要管理员权限和实际驱动
        // 但测试异步包装器的创建
        if let Ok(adapter) = AdapterBuilder::new()
            .name("TestAsyncAdapter")
            .tunnel_type("VelociTun")
            .build()
        {
            if let Ok(session) = adapter.start_session(0x400000) {
                let async_session = AsyncSession::new(session);

                // 测试基本属性
                assert!(async_session.capacity() > 0);

                // 测试异步发送接收器创建
                let _sender = async_session.async_packet_sender();
                let _receiver = async_session.async_packet_receiver();

                // 测试通道创建
                let (_tx, _rx) = async_session.create_packet_channel(10);
            }
        }
    }

    #[test]
    async fn test_packet_stream() {
        use futures::StreamExt;

        // 这个测试需要实际的会话
        // 但测试流接口的编译
        let test_data = create_test_packet();
        assert!(test_data.len() > 0);
    }
}
