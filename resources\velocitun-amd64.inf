[Version]
Signature = "$Windows NT$"
Class = Net
ClassGUID = {4d36e972-e325-11ce-bfc1-08002be10318}
Provider = %ManufacturerName%
CatalogFile = velocitun.cat
DriverVer = 01/01/2024,********
PnpLockdown = 1

[Manufacturer]
%ManufacturerName% = Standard,NTamd64,NTarm64

[Standard.NTamd64]
%DeviceDescription% = velocitun.ndi, VelociTun

[Standard.NTarm64]
%DeviceDescription% = velocitun.ndi, VelociTun

[velocitun.ndi]
Characteristics = 0x1
*IfType = 0x1
*MediaType = 0x0
*PhysicalMediaType = 0x0
AddReg = velocitun.ndi.AddReg
CopyFiles = velocitun.CopyFiles

[velocitun.ndi.AddReg]
HKR, , BusNumber, 0, "0"

[velocitun.CopyFiles]
velocitun.sys,,,2

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
velocitun.sys = 1,,

[DestinationDirs]
velocitun.CopyFiles = 12

[Strings]
ManufacturerName = "VelociTun"
DeviceDescription = "VelociTun High-Speed Network Tunnel"
DiskName = "VelociTun Driver Disk"
